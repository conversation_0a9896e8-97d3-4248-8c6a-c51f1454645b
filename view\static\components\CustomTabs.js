const OTHER = "Box+Other"
const FOODTYPE = "Box+FoodType"
const ALL = "AllFoodType"
const SELECTED = "SelectedFoodType"
const OTHERCODE = "_OTHER_"

const CustomTabs = Vue.component("custom-tabs", {
  props: {
    combinedTabs: Boolean, // 是否有组合tabs配置
    tabsConfig: Object, // 组合tabs配置
    tabsActive: String, // 用于baseTab的active或combinedTab的第二层active
    tabsList: Array, //tabs 数据
    mergeMap: Object,
    removeItemFn: Function,
    hideItemFn: Function,
    formatName: Function,
    language: Object
  },
  watch: {
    tabsList(val) {
      this.list = val
    },
    tabsActive(val) {
      this.resetFrActive()
      this.moveSlider()
    },
    // 切換語言時,重新定位滑條
    language() {
      this.moveSlider()
    }
  },
  data() {
    return {
      list: [], //tabs源数据
      frTabActive: null // 第一级tabs 选中值
    }
  },
  computed: {
    activeTabStyle() {
      let { color } = this.tabsConfig
      if (color) {
        return color
      }
      return "var(--styleColor)"
    },
    // 排除合并盒子<boxList>后的list
    listExcludedBox() {
      let codes = Object.keys(this.mergeMap)
      return this.list.filter(el => !codes.includes(el.code))
    },
    // 所有的合并盒子
    boxList() {
      let codes = Object.keys(this.mergeMap)
      return this.list.filter(el => codes.includes(el.code))
    },
    // 所有的被合并的foodType
    mergeCodes() {
      return [...new Set(Object.values(this.mergeMap).flat(3))]
    },
    // 渲染的第一行tab的list
    firstRowTabList() {
      let { tab1, tab2 } = this.tabsConfig
      if (tab1 === OTHER) {
        const other = {
          code: OTHERCODE,
          name: this.language.other || "OTHER"
        }
        return [...this.boxList, other]
      }
      // if (tab2 === ALL) {
      //   return this.list
      // }
      // 当tab2仅显示合并项时,tab1不显示合并项
      return this.list.filter(e => !this.mergeCodes.includes(e.code))
    },
    //渲染的第二行tab的list
    secondRowTabList() {
      const { tab1, tab2 } = this.tabsConfig
      if (tab2 === ALL) {
        return this.list
      } else {
        //仅显示盒子下的数据
        const frActiveMergeCode = this.mergeMap[this.frTabActive]
        // 第一级为盒子
        if (Array.isArray(frActiveMergeCode)) {
          // 若盒子本身存在flist,也要加入盒子
          const box = this.boxList.find(e => e.code === this.frTabActive)
          const addBox = box && box.foodList && box.foodList.length
          return [addBox && box, ...this.list.filter(e => frActiveMergeCode.includes(e.code))]
        }
        if (tab1 === ALL) {
          // 只显示第一级选中的type
          return this.list.filter(e => e.code === this.frTabActive)
        }
        // 返回不包括盒子及盒子下的fty
        return this.listExcludedBox.filter(e => !this.mergeCodes.includes(e.code))
      }
    }
  },
  methods: {
    // 点击原Tabs item
    onBaseTab(item, index) {
      this.$emit("on-tab", item)
    },
    // 点击组合后的第一行tab
    onFrTab(item) {
      // 重置tabsActive:第二层tab
      // 若frTabActive为盒子,则选中盒子下的第一个fty
      // 若为其他,选中非被合并的第一个fty;
      // 若为其他fty;则选中该fty
      let renderTargetCode = null

      let boxIds = Object.keys(this.mergeMap)
      if (boxIds.includes(item.code)) {
        //为盒子
        // 若盒子存在flist,则应该选中盒子;removeItemFn:hasFoodList
        if (this.removeItemFn(item)) {
          renderTargetCode = item.code
        } else {
          let allItems = this.list.filter(e => this.mergeMap[item.code].includes(e.code))
          renderTargetCode = allItems.length && allItems[0].code
        }
      } else {
        if (item.code === OTHERCODE) {
          const remain = this.listExcludedBox.filter(ty => !this.mergeCodes.includes(ty.code))
          renderTargetCode = remain.length && remain[0].code
        } else {
          // 为fty
          renderTargetCode = item.code
        }
      }
      //renderTargetCode = this.removeItemFn(item) ? item.code : this.mergeMap[this.frTabActive][0]
      let target = this.list.find(t => t.code === renderTargetCode)
      this.onBaseTab(target || item)
    },
    // 维护第一级tab:frTabActive
    resetFrActive() {
      let boxIds = Object.keys(this.mergeMap)
      // 组合tabs
      if (this.combinedTabs) {
        let { tab1, tab2 } = this.tabsConfig
        // 判断tabsActive是否被合并到盒子内
        if (this.mergeCodes.includes(this.tabsActive)) {
          let typeCode = null
          // 找到父盒子
          for (const tc in this.mergeMap) {
            if (this.mergeMap[tc].includes(this.tabsActive)) {
              typeCode = tc
              break
            }
          }
          // 选中第一级tab
          if (typeCode) {
            this.frTabActive = typeCode
          }
        } else if (boxIds.includes(this.tabsActive)) {
          // 选中的为盒子本身
          let exist = this.list.find(e => e.code === this.tabsActive)
          if (exist && this.hideItemFn(exist) && this.removeItemFn(exist)) {
            this.frTabActive = this.tabsActive
          } else {
            // 不存在则该盒子下没有foodList,被过滤了;则选中盒子下的第一个type
            this.onFrTab({ code: this.tabsActive })
          }
        } else {
          // 未被合并到盒子中,则为OTHER或者Fty
          if (tab1 === OTHER) {
            this.frTabActive = OTHERCODE
            // 无需平滑滚动
            return false
          } else {
            this.frTabActive = this.tabsActive
          }
        }

        let selector = `.fr_tabs #fty-${this.frTabActive}`
        this.smoothScrolling(selector)
      } else {
        // 无需处理
      }
    },
    /**
     * @description 点击组合tabs的item
     * @param level {1|2} 点击的是tabs的层级
     * @param item item
     * */
    onCombinedTab(level, item) {
      let secTab = level === 2
      if (secTab) {
        if (this.tabsActive === item.code) return false
        this.onBaseTab(item)
      } else {
        if (this.frTabActive === item.code) return false
        this.onFrTab(item)
      }
    },
    formatTypeName(item) {
      if (item.code === OTHERCODE) {
        return this.language.other
      }
      return this.formatName(item)
    },
    // 平滑滚动
    smoothScrolling(selector) {
      const scroll = DOM => {
        if (DOM instanceof HTMLElement) {
          DOM.scrollIntoView({
            inline: "center"
          })
        }
      }
      this.$nextTick(() => {
        if (typeof selector === "string") {
          const DOM = document.querySelector(selector)
          scroll(DOM)
        } else {
          scroll(selector)
        }
      })
    },
    moveSlider() {
      if (!this.combinedTabs) {
        let selector = ".tab_active.tab_cell"
        // this.smoothScrolling(selector)
        return false
      }
      let tab1Selector = `.fr_tabs .tab_active`
      let tab2Selector = `.sec_tabs .tab_active`
      const toSelect = selector => {
        const DOM = document.querySelector(selector)
        if (DOM instanceof HTMLElement) {
          let { offsetWidth, offsetLeft } = DOM
          let sliderSelector = selector.split(" .")[0] + " .tabs-slider-wrapper"
          let sliderDOM = document.querySelector(sliderSelector)
          sliderDOM.style.setProperty("width", offsetWidth + "px")
          sliderDOM.style.setProperty("left", offsetLeft + "px")
          this.smoothScrolling(DOM)
        }
      }
      this.smoothScrolling(tab1Selector)
      this.smoothScrolling(tab2Selector)
      // this.$nextTick(() => {
      // toSelect(tab1Selector)
      // toSelect(tab2Selector)
      // })
    },
    // 何时该隐藏第一级tabs的item
    showFrTab(item) {
      return (
        Array.isArray(this.mergeMap[item.code]) ||
        (this.tabsConfig.tab1 === ALL && this.removeItemFn(item))
      )
    },
    setStyle(type, code) {
      if (this.frTabActive && this.frTabActive) {
        let isFirst = type === 1
        let active = isFirst ? code === this.frTabActive : code === this.tabsActive
        return `backgroundColor:${active ? this.activeTabStyle : "unset"}`
      }
    }
  },
  template: `
    <div class="combined_tab_warp" v-if="combinedTabs">
        <ul class="fr_tabs">
<!--           <div class="tabs-slider-wrapper"> -->
<!--             <div class="tabs-slider"></div> -->
<!--           </div> -->
          <li v-for="item in firstRowTabList" :key="item.code"
              :id="'fty-'+item.code"
              :class="[{ tab_active: frTabActive === item.code }, 'tab_cell']"
               v-show="item.code===OTHERCODE||hideItemFn(item)"
               v-if="item.code===OTHERCODE||showFrTab(item)"
               @click="onCombinedTab(1,item)"
              >
            <!-- v-show: OTHER无需验证会员 -->
            <span :style="setStyle(1,item.code)">{{formatTypeName(item)}}</span> 
          </li>
        </ul>
        <ul class="sec_tabs" >  
<!--           <div class="tabs-slider-wrapper"> -->
<!--             <div class="tabs-slider"></div> -->
<!--           </div> -->
          <li v-for="item in secondRowTabList" :key="item.code" 
              :id="'fty-'+item.code"
              :class="[{ tab_active: tabsActive === item.code }, 'tab_cell']"
              v-if="removeItemFn(item)"
              v-show="hideItemFn(item)"
              @click="onCombinedTab(2,item)"
              >
            <span :style="setStyle(2,item.code)">{{formatName(item)}}</span>
          </li>
        </ul>
    </div>    
    <!-- 原滚动Tab头  -->
    <div class="tab_warp" v-else>
      <template v-for="(item, index) in list" :key="item.code">
        <div
          :class="[{ tab_active: tabsActive == item.code }, 'tab_cell']"
          @click="onBaseTab(item)"
          :id="'fty-'+item.code"
          v-if="removeItemFn(item)"
          v-show="hideItemFn(item)"
        >
          {{formatName(item)}}
        </div>
      </template>
    </div>
  `
})
